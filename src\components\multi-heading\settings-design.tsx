// External dependencies.
import React, { ReactElement, useState } from 'react';

// WordPress dependencies.
import { __ } from '@wordpress/i18n';

// Divi dependencies.
import {
  AnimationGroup,
  BorderGroup,
  BoxShadowGroup,
  FiltersGroup,
  FontGroup,
  FontBodyGroup,
  SizingGroup,
  SpacingGroup,
  TransformGroup,
  BackgroundGroup,
  FieldContainer,
} from '@divi/module';
import {
  type Module,
} from '@divi/types';
import { GroupContainer, GroupTabs } from '@divi/modal';

// Local dependencies.
import { MultiHeadingAttrs } from "./types";
import { ColorPickerContainer, RangeContainer, SelectContainer } from '@divi/field-library';

export const SettingsDesign = ({
  defaultSettingsAttrs, ...props
}: Module.Settings.Panel.Props<MultiHeadingAttrs>): ReactElement => {
  const {
    attrs,
  } = props;

  const [activeTab, setAcivteTab] = useState('front');


  const displayOption = attrs?.display?.advanced?.position?.desktop?.value ?? '';
  const title1Style = attrs?.title1?.advanced?.style?.desktop?.value ?? '';
  const title2Style = attrs?.title2?.advanced?.style?.desktop?.value ?? '';
  const title3Style = attrs?.title3?.advanced?.style?.desktop?.value ?? '';
  return (
    <React.Fragment>
      <GroupContainer id="textStyle" title={__('Container Style', 'optimaizer-modules')}>
        {/* <FieldContainer
          attrName="display.advanced.position"
          defaultAttr={defaultSettingsAttrs?.display?.advanced?.position}
          label={__('Display Type', 'optimaizer-modules')}
          description={__('Input your value to action.', 'optimaizer-modules')}
          features={{
            sticky: false,
            dynamicContent: {
              type: 'text',
            },
          }}
        >
          <SelectContainer
            options={{
              "block": { label: 'Block – Full width' },
              "flex": { label: 'Flex – Align items' },
              "inline-block": { label: 'Inline Block – Small blocks' },
              "inline": { label: 'Inline – Same line' },
              "inline-flex": { label: 'Inline Flex – Align inline' },
              "none": { label: 'None – Hide' },
            }}

            grouped={false}
          />
        </FieldContainer>

        <FieldContainer
          attrName="display.advanced.flexDirection"
          defaultAttr={defaultSettingsAttrs?.display?.advanced?.flexDirection}
          label={__('Display Direction', 'optimaizer-modules')}
          description={__('Input your value to action.', 'optimaizer-modules')}
          visible={displayOption === 'flex' || displayOption === 'inline-flex'}
          features={{
            sticky: false,
            dynamicContent: {
              type: 'text',
            },
          }}
        >
          <SelectContainer
            options={{
              "row": { label: 'Row' },
              "row-reverse": { label: 'Row Reverse' },
              "column": { label: 'Column' },
              "column-reverse": { label: 'Column Reverse' }
            }}
            grouped={false}
          />
        </FieldContainer>
        <FieldContainer
          attrName="display.advanced.alignItems"
          defaultAttr={defaultSettingsAttrs?.display?.advanced?.alignItems}
          visible={displayOption === 'flex' || displayOption === 'inline-flex'}
          label={__('Horizontal Alignment', 'optimaizer-modules')}
          description={__('Choose how to align items horizontally.', 'optimaizer-modules')}
          features={{
            sticky: false,
            dynamicContent: {
              type: 'text',
            },
          }}
        >
          <SelectContainer
            options={{
              "flex-start": { label: 'Flex Start' },
              "center": { label: 'Center' },
              "space-between": { label: 'Space Between' },
              "space-around": { label: 'Space Around' },
              "space-evenly": { label: 'Space Evenly' },
              "flex-end": { label: 'Flex End' },
            }}
            grouped={false}
          />
        </FieldContainer>

        <FieldContainer
          attrName="display.advanced.justifyContent"
          defaultAttr={defaultSettingsAttrs?.display?.advanced?.justifyContent}
          visible={displayOption === 'flex' || displayOption === 'inline-flex'}
          label={__('Vertical Alignment', 'optimaizer-modules')}
          description={__('Choose how to align items vertically.', 'optimaizer-modules')}
          features={{
            sticky: false,
            dynamicContent: {
              type: 'text',
            },
          }}
        >
          <SelectContainer
            options={{
              "flex-start": { label: 'Flex Start' },
              "flex-end": { label: 'Flex End' },
              "center": { label: 'Center' },
              "baseline": { label: 'Baseline' },
              "stretch": { label: 'Stretch' },
              "start": { label: 'Start' },
              "end": { label: 'End' },
              "self-start": { label: 'Self Start' },
              "self-end": { label: 'Self End' },
            }}
            grouped={false}
          />
        </FieldContainer> */}

        <FieldContainer
          attrName="title1.advanced.style"
          label={__('First Heading Style', 'optimaizer-modules')}
          description={__('Input your value to style the first heading.', 'optimaizer-modules')}
          features={{
            sticky: false,
            dynamicContent: {
              type: 'text',
            },
          }}
          visible={activeTab === 'back'}
        >
          <SelectContainer
            options={{
              selectoption: { label: __('Select Option', 'optimaizer-modules') },
              gradient: { label: __('Gradient', 'optimaizer-modules') },
              image: { label: __('Background Image', 'optimaizer-modules') },
              stock: { label: __('Stock', 'optimaizer-modules') },
            }}

            grouped={false}
          />
        </FieldContainer>

        <BackgroundGroup attrName="container.decoration.background" grouped={false} />
        <SpacingGroup
          attrName="container.decoration.spacing"
          grouped={false}
        />
      </GroupContainer>

      <GroupContainer id="textStyle1" title={__('First Heading Style', 'optimaizer-modules')}>
        <GroupTabs
          showLabel={true}
          activeTab={activeTab}
          showIcon={false}
          tabs={{
            'front': {
              "label": "Front Style",
              "icon": "iconNameFor2D" // Replace "iconNameFor2D" with the actual icon identifier for the 2D tab
            },
            'back': {
              "label": "Heading Style",
              "icon": "iconNameForBG" // Replace "iconNameForBG" with the actual icon identifier for the BG tab
            }

          }}
          onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
            let target = e.target as HTMLButtonElement;
            let parent = target.parentElement;
            setAcivteTab(
              target.value ? target.value : (parent as HTMLButtonElement).value
            );
          }}
        />
        <FieldContainer
          attrName="title1.advanced.display"
          defaultAttr={defaultSettingsAttrs?.title1?.advanced?.display}
          label={__('Display Type', 'optimaizer-modules')}
          description={__('Input your value to action title here.', 'optimaizer-modules')}
          features={{
            sticky: false,
            dynamicContent: {
              type: 'text',
            },
          }}
          visible={activeTab === 'front' ? true : false}

        >

          <SelectContainer
            options={{
              "selectoption": { label: 'Select Option' },
              "block": { label: 'Block' },
              "inline-block": { label: 'Inline Block' },
              "inline": { label: 'Inline' },
            }}
            defaultValue="block"
            grouped={false}
          />

        </FieldContainer>

        <FontGroup
          groupLabel={__('Typography', 'optimaizer-modules')}
          attrName="title1.decoration.font"
          fieldLabel={__('First Heading', 'optimaizer-modules')}
          fields={{
            headingLevel: {
              render: true,
            },
          }}
          defaultGroupAttr={defaultSettingsAttrs?.title1?.decoration?.font}
          grouped={false}
          visible={activeTab === 'front' ? true : false}
        />
        <BorderGroup attrName="title1.decoration.border" grouped={false} visible={activeTab === 'front' ? true : false} />
        {/* <BackgroundGroup attrName="title1.decoration.background" grouped={false} visible={activeTab === 'front' ? true : false} /> */}
        <SpacingGroup attrName="title1.decoration.spacing" grouped={false} visible={activeTab === 'front' ? true : false} />

        <FieldContainer
          attrName="title1.advanced.style"
          label={__('First Heading Style', 'optimaizer-modules')}
          defaultAttr={defaultSettingsAttrs?.title1?.advanced?.style}
          description={__('Input your value to action title here.', 'optimaizer-modules')}
          features={{
            sticky: false,
            dynamicContent: {
              type: 'text',
            },
          }}
          visible={activeTab === 'back' ? true : false}

        >
          <SelectContainer
            options={{
              "selectoption": { label: 'Select Option' },
              "gradient": { label: 'Gradient' },
              "image": { label: 'Background Image' },
              "stock": { label: 'Stock' },
            }}
            grouped={false}
          />

        </FieldContainer>

        <BackgroundGroup
          attrName="title1.decoration.background"
          hidePanels={['video', 'pattern', 'image', 'mask', 'color']}
          groupLabel="Text Gradient Colors"
          grouped={false}
          visible={activeTab === 'back' && title1Style === 'gradient' ? true : false}
        />
        <BackgroundGroup
          attrName="title1.decoration.background"
          hidePanels={['video', 'pattern', '', 'gradient', 'mask', 'color']}
          groupLabel="Text Gradient Colors"
          grouped={false}
          visible={activeTab === 'back' && title1Style === 'image' ? true : false}
        />

        <FieldContainer
          attrName="title1.advanced.animation"
          label={__('First Heading Animation speed', 'optimaizer-modules')}
          defaultAttr={defaultSettingsAttrs?.title1?.advanced?.animation}
          description={__('Input your value to action title here.', 'optimaizer-modules')}
          features={{
            sticky: false,
            dynamicContent: {
              type: 'text',
            },
          }}
          visible={activeTab === 'back' && title1Style === 'image' ? true : false}
        >
          <RangeContainer
            defaultUnit="s" max={100} min={0} step={1}
          />
        </FieldContainer>

        <FieldContainer
          attrName="title1.advanced.strock"
          label={__('First Heading Stock Color', 'optimaizer-modules')}
          defaultAttr={defaultSettingsAttrs?.title1?.advanced?.stock}
          description={__('Input your value to set the stock color for the first heading.', 'optimaizer-modules')}
          features={{
            sticky: false,
            dynamicContent: {
              type: 'text',
            },
          }}
          visible={activeTab === 'back' && title1Style === 'stock'}
        >
          <ColorPickerContainer />
        </FieldContainer>

        <FieldContainer
          attrName="title1.advanced.borderWidth"
          label={__('First Heading Border Width', 'optimaizer-modules')}
          defaultAttr={defaultSettingsAttrs?.title1?.advanced?.borderWidth}
          description={__('Adjust the border (stroke) width for the first heading.', 'optimaizer-modules')}

          visible={activeTab === 'back' && title1Style === 'stock'}
        >
          <RangeContainer defaultUnit="px" max={100} min={0} step={1} />

        </FieldContainer>
      </GroupContainer>



      <GroupContainer id="textStyle2" title={__('Second Heading Style', 'optimaizer-modules')}>
        <GroupTabs
          showLabel={true}
          activeTab={activeTab}
          showIcon={false}
          tabs={{
            'front': {
              "label": "Front Style",
              "icon": "iconNameFor2D" // Replace "iconNameFor2D" with the actual icon identifier for the 2D tab
            },
            'back': {
              "label": "Heading Style",
              "icon": "iconNameForBG" // Replace "iconNameForBG" with the actual icon identifier for the BG tab
            }

          }}
          onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
            let target = e.target as HTMLButtonElement;
            let parent = target.parentElement;
            setAcivteTab(
              target.value ? target.value : (parent as HTMLButtonElement).value
            );
          }}
        />
        <FieldContainer
          attrName="title2.advanced.display"
          defaultAttr={defaultSettingsAttrs?.title2?.advanced?.display}
          label={__('Display Type', 'optimaizer-modules')}
          description={__('Input your value to action title here.', 'optimaizer-modules')}
          features={{
            sticky: false,
            dynamicContent: {
              type: 'text',
            },
          }}
          visible={activeTab === 'front' ? true : false}

        >

          <SelectContainer
            options={{
              "selectoption": { label: 'Select Option' },
              "block": { label: 'Block' },
              "inline-block": { label: 'Inline Block' },
              "inline": { label: 'Inline' },
            }}
            grouped={false}
          />

        </FieldContainer>
        <FontGroup
          groupLabel={__('Typography', 'optimaizer-modules')}
          attrName="title2.decoration.font"
          fieldLabel={__('Second Heading', 'optimaizer-modules')}
          fields={{
            headingLevel: {
              render: true,
            },
          }}
          defaultGroupAttr={defaultSettingsAttrs?.title2?.decoration?.font}
          grouped={false}
          visible={activeTab === 'front' ? true : false}
        />
        <BorderGroup attrName="title2.decoration.border" grouped={false} visible={activeTab === 'front' ? true : false} />
        <SpacingGroup attrName="title2.decoration.spacing" grouped={false} visible={activeTab === 'front' ? true : false} />


        <FieldContainer
          attrName="title2.advanced.style"
          label={__('Second Heading Style', 'optimaizer-modules')}
          description={__('Input your value to style the first heading.', 'optimaizer-modules')}
          defaultAttr={defaultSettingsAttrs?.title2?.advanced?.style}
          features={{
            sticky: false,
            dynamicContent: {
              type: 'text',
            },
          }}
          visible={activeTab === 'back'}
        >
          <SelectContainer
            options={{
              selectoption: { label: __('Select Option', 'optimaizer-modules') },
              gradient: { label: __('Gradient', 'optimaizer-modules') },
              image: { label: __('Background Image', 'optimaizer-modules') },
              stock: { label: __('Stock', 'optimaizer-modules') },
            }}

            grouped={false}
            defaultValue="Select Option"
          />
        </FieldContainer>

        <BackgroundGroup
          attrName="title2.decoration.background"
          hidePanels={['video', 'pattern', 'image', 'mask', 'color']}
          groupLabel="Text Gradient Colors"
          grouped={false}
          visible={activeTab === 'back' && title2Style === 'gradient' ? true : false}
        />
        <BackgroundGroup
          attrName="title2.decoration.background"
          hidePanels={['video', 'pattern', '', 'gradient', 'mask', 'color']}
          groupLabel="Text Gradient Colors"
          grouped={false}
          visible={activeTab === 'back' && title2Style === 'image' ? true : false}
        />

        <FieldContainer
          attrName="title2.advanced.animation"
          label={__('Second Heading Animation speed', 'optimaizer-modules')}
          defaultAttr={defaultSettingsAttrs?.title2?.advanced?.animation}
          description={__('Input your value to action title here.', 'optimaizer-modules')}
          features={{
            sticky: false,
            dynamicContent: {
              type: 'text',
            },
          }}
          visible={activeTab === 'back' && title2Style === 'image' ? true : false}
        >
          <RangeContainer
            defaultUnit="s" max={100} min={0} step={1}
          />
        </FieldContainer>

        <FieldContainer
          attrName="title2.advanced.strock"
          label={__('Second Heading Stock Color', 'optimaizer-modules')}
          defaultAttr={defaultSettingsAttrs?.title2?.advanced?.stock}
          description={__('Input your value to set the stock color for the first heading.', 'optimaizer-modules')}
          features={{
            sticky: false,
            dynamicContent: {
              type: 'text',
            },
          }}
          visible={activeTab === 'back' && title2Style === 'stock'}
        >
          <ColorPickerContainer />
        </FieldContainer>

        <FieldContainer
          attrName="title2.advanced.borderWidth"
          label={__('Second Heading Border Width', 'optimaizer-modules')}
          defaultAttr={defaultSettingsAttrs?.title2?.advanced?.borderWidth}
          description={__('Adjust the border (stroke) width for the first heading.', 'optimaizer-modules')}

          visible={activeTab === 'back' && title2Style === 'stock'}
        >
          <RangeContainer defaultUnit="px" max={100} min={0} step={1} />
        </FieldContainer>

      </GroupContainer>




      <GroupContainer id="textStyle3" title={__('Third Heading Style', 'optimaizer-modules')}>
        <GroupTabs
          showLabel={true}
          activeTab={activeTab}
          showIcon={false}
          tabs={{
            'front': {
              "label": "Front Style",
              "icon": "iconNameFor2D" // Replace "iconNameFor2D" with the actual icon identifier for the 2D tab
            },
            'back': {
              "label": "Heading Style",
              "icon": "iconNameForBG" // Replace "iconNameForBG" with the actual icon identifier for the BG tab
            }

          }}
          onClick={(e: React.MouseEvent<HTMLButtonElement>) => {
            let target = e.target as HTMLButtonElement;
            let parent = target.parentElement;
            setAcivteTab(
              target.value ? target.value : (parent as HTMLButtonElement).value
            );
          }}
        />
        <FieldContainer
          attrName="title3.advanced.display"
          defaultAttr={defaultSettingsAttrs?.title3?.advanced?.display}
          label={__('Display Type', 'optimaizer-modules')}
          description={__('Input your value to action title here.', 'optimaizer-modules')}
          features={{
            sticky: false,
            dynamicContent: {
              type: 'text',
            },
          }}
          visible={activeTab === 'front' ? true : false}

        >

          <SelectContainer
            options={{
              "selectoption": { label: 'Select Option' },
              "block": { label: 'Block' },
              "inline-block": { label: 'Inline Block' },
              "inline": { label: 'Inline' },
            }}
            defaultValue="block"
            grouped={false}
          />

        </FieldContainer>
        <FontGroup
          groupLabel={__('Typography', 'optimaizer-modules')}
          attrName="title3.decoration.font"
          fieldLabel={__('Third Heading', 'optimaizer-modules')}
          fields={{
            headingLevel: {
              render: true,
            },
          }}
          defaultGroupAttr={defaultSettingsAttrs?.title3?.decoration?.font}
          grouped={false}
          visible={activeTab === 'front' ? true : false}
        />
        <BorderGroup attrName="title3.decoration.border" grouped={false}  visible={activeTab === 'front' ? true : false} />
        <SpacingGroup attrName="title3.decoration.spacing" grouped={false}  visible={activeTab === 'front' ? true : false} />

        <FieldContainer
          attrName="title3.advanced.style"
          label={__('Third Heading Style', 'optimaizer-modules')}
          description={__('Input your value to style the first heading.', 'optimaizer-modules')}
          defaultAttr={defaultSettingsAttrs?.title3?.advanced?.style}
          features={{
            sticky: false,
            dynamicContent: {
              type: 'text',
            },
          }}
          visible={activeTab === 'back'}
        >
          <SelectContainer
            options={{
              selectoption: { label: __('Select Option', 'optimaizer-modules') },
              gradient: { label: __('Gradient', 'optimaizer-modules') },
              image: { label: __('Background Image', 'optimaizer-modules') },
              stock: { label: __('Stock', 'optimaizer-modules') },
            }}

            grouped={false}
          />
        </FieldContainer>

        <BackgroundGroup
          attrName="title3.decoration.background"
          hidePanels={['video', 'pattern', 'image', 'mask', 'color']}
          groupLabel="Text Gradient Colors"
          grouped={false}
          visible={activeTab === 'back' && title3Style === 'gradient' ? true : false}
        />
        <BackgroundGroup
          attrName="title3.decoration.background"
          hidePanels={['video', 'pattern', '', 'gradient', 'mask', 'color']}
          groupLabel="Text Gradient Colors"
          grouped={false}
          visible={activeTab === 'back' && title3Style === 'image' ? true : false}
        />
        <FieldContainer
          attrName="title3.advanced.animation"
          label={__('Third Heading Animation speed', 'optimaizer-modules')}
          defaultAttr={defaultSettingsAttrs?.title3?.advanced?.animation}
          description={__('Input your value to action title here.', 'optimaizer-modules')}
          features={{
            sticky: false,
            dynamicContent: {
              type: 'text',
            },
          }}
          visible={activeTab === 'back' && title3Style === 'image' ? true : false}
        >
          <RangeContainer
            defaultUnit="s" max={100} min={0} step={1}
          />
        </FieldContainer>

        <FieldContainer
          attrName="title3.advanced.strock"
          label={__('Third Heading Stock Color', 'optimaizer-modules')}
          defaultAttr={defaultSettingsAttrs?.title3?.advanced?.stock}
          description={__('Input your value to set the stock color for the first heading.', 'optimaizer-modules')}
          features={{
            sticky: false,
            dynamicContent: {
              type: 'text',
            },
          }}
          visible={activeTab === 'back' && title3Style === 'stock'}
        >
          <ColorPickerContainer />
        </FieldContainer>

        <FieldContainer
          attrName="title3.advanced.borderWidth"
          label={__('Third Heading Border Width', 'optimaizer-modules')}
          defaultAttr={defaultSettingsAttrs?.title3?.advanced?.borderWidth}
          description={__('Adjust the border (stroke) width for the first heading.', 'optimaizer-modules')}

          visible={activeTab === 'back' && title3Style === 'stock'}
        >
          <RangeContainer defaultUnit="px" max={100} min={0} step={1} />
        </FieldContainer>


      </GroupContainer>
      <SizingGroup />
      <SpacingGroup />
      <BorderGroup />
      <BoxShadowGroup />
      <FiltersGroup />
      <TransformGroup />
      <AnimationGroup />
    </React.Fragment>
  )
}
