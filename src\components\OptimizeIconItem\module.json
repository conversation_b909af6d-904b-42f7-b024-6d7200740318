{"name": "dotm/optimize-icon-item", "d4Shortcode": "", "title": "Optimize Icon Item", "titles": "Optimize Icon Items", "category": "child-module", "attributes": {"module": {"type": "object", "selector": "{{selector}}", "default": {"meta": {"adminLabel": {"desktop": {"value": "Optimize Icon Item"}}}}, "styleProps": {"border": {"important": true}, "spacing": {"important": true}}}, "title": {"type": "object", "selector": ".dotm_optimize_icon_list {{selector}} .dotm_optimize_icon_item_title", "styleProps": {"font": {"important": {"font": {"desktop": {"value": {"color": true}}}}}}, "tagName": "div", "attributes": {"class": "dotm_optimize_icon_item_title"}, "inlineEditor": "plainText", "elementType": "text", "childrenSanitizer": "et_core_esc_previously"}, "tooltip": {"type": "object", "selector": ".dotm_optimize_icon_list {{selector}} .dotm_optimize_icon_item_tooltip_text", "defaultPrintedStyle": {"decoration": {"bgColor": {"desktop": {"value": "#9c27b0"}}}}, "default": {"decoration": {"position": {"desktop": {"value": "right"}}}}, "tagName": "div", "attributes": {"class": "dotm_optimize_icon_item_tooltip_text"}, "inlineEditor": "richText", "childrenSanitizer": "et_core_esc_previously"}, "image_container": {"type": "object", "selector": ".dotm_optimize_icon_list {{selector}} .dotm_optimize_icon_item_image_container"}, "image": {"type": "object", "selector": ".dotm_optimize_icon_list {{selector}} .dotm_optimize_icon_item_image", "defaultPrintedStyle": {"advanced": {"size": {"desktop": {"value": "35px"}}}}, "elementType": "image", "tagName": "img", "inlineEditor": "image", "childrenSanitizer": "et_core_esc_previously", "allowHtml": true, "attributes": {"class": "dotm_optimize_icon_item_image"}}, "icon": {"type": "object", "selector": ".dotm_optimize_icon_list {{selector}} .dotm_optimize_icon_item_icon", "default": {"innerContent": {"desktop": {"value": {"unicode": "&#x39;", "type": "divi", "weight": "400"}}}, "advanced": {"use_icon": {"desktop": {"value": "on"}}}}, "defaultPrintedStyle": {"advanced": {"color": {"desktop": {"value": "#ae16f0"}}, "size": {"desktop": {"value": "28px"}}}}}}, "customCssFields": {"contentContainer": {"subName": "contentContainer", "selectorSuffix": " .example_child_module__content-container"}, "title": {"subName": "title", "selectorSuffix": " .example_child_module__title"}, "content": {"subName": "content", "selectorSuffix": " .example_child_module__content"}, "icon": {"subName": "icon", "selectorSuffix": " .example_child_module__icon.et-pb-icon"}}}