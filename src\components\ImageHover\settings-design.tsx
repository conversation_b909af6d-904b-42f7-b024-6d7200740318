// External dependencies.
import React, { ReactElement } from 'react';

// WordPress dependencies.
import { __ } from '@wordpress/i18n';

// Divi dependencies.
import {
  AnimationGroup,
  BorderGroup,
  BoxShadowGroup,
  Field,
  FieldContainer,
  FiltersGroup,
  FontBodyGroup,
  SizingGroup,
  SpacingGroup,
  TransformGroup,
} from '@divi/module';
import {
  type Module,
} from '@divi/types';
import { GroupContainer } from '@divi/modal';

// Local dependencies.
import { ImageHoverAttrs } from "./types";
import { ColorPickerContainer, Range, RangeContainer, Select, SelectContainer } from '@divi/field-library';
const handleRangeChange = (params: { inputValue: string }) => {
  // setRangeValue(value);
  console.log('Range Value:', params.inputValue);
};


export const SettingsDesign = ({
  defaultSettingsAttrs, ...props
}: Module.Settings.Panel.Props<ImageHoverAttrs>): ReactElement => {
  const { attrs, } = props;

  const holographicHorizontal = attrs?.hover?.decoration?.hoverClass?.desktop?.value ?? '';
  const holographicVertical = attrs?.hover?.decoration?.hoverClass?.desktop?.value ?? '';
  const rippleEffect = attrs?.hover?.decoration?.hoverClass?.desktop?.value ?? '';
  const effectBorder1 = attrs?.hover?.decoration?.hoverClass?.desktop?.value ?? '';
  const effectBorder2 = attrs?.hover?.decoration?.hoverClass?.desktop?.value ?? '';
  const magneticCorner = attrs?.hover?.decoration?.hoverClass?.desktop?.value ?? '';
  const opacity = attrs?.hover?.decoration?.hoverClass?.desktop?.value ?? '';

  return (
    <React.Fragment>
      <GroupContainer id="imageStyle" title={__('Image Style', 'divi-optimaizer-modules')}>
        <FieldContainer
          attrName="image.decoration.imagewidth"
          label={__('Image Width', 'divi-optimaizer-modules')}
          defaultAttr={defaultSettingsAttrs?.image?.decoration?.imagewidth}
          description={__('Input your value to action here.', 'divi-optimaizer-modules')}
          features={{
            sticky: false,
          }}
        >
          <RangeContainer min={0} max={100} defaultUnit="%" />
        </FieldContainer>

        <FieldContainer
          attrName="image.decoration.imageHeight"
          label={__('Image Height', 'divi-optimaizer-modules')}
          defaultAttr={defaultSettingsAttrs?.image?.decoration?.imageHeight}
          description={__('Input your value to action here.', 'divi-optimaizer-modules')}
          features={{
            sticky: false,
          }}
        >
          <RangeContainer min={0} max={100} defaultUnit="%" />
        </FieldContainer>

        <FieldContainer
          attrName="image.decoration.imageSize"
          label={__('Image Size', 'divi-optimaizer-modules')}
          defaultAttr={defaultSettingsAttrs?.image?.decoration?.imageSize}
          description={__('Input your value to action here.', 'divi-optimaizer-modules')}
          features={{
            sticky: false,
          }}
        >
          <SelectContainer
            options={{
              'cover': { label: 'Cover' },
              'contain': { label: 'Contain' },
              'initial': { label: 'Initial' },
              'none': { label: 'None' },
              'auto': { label: 'Auto' },
              'scale-down': { label: 'Scale Down' },
              'scale-up': { label: 'Scale Up' },
              'fill': { label: 'Fill' },
            }}
          />

        </FieldContainer>
        <BorderGroup
          attrName="image.decoration.border"
          grouped={false}
        />
        <SpacingGroup
          attrName="image.decoration.spacing"
          grouped={false}
        />
        <BoxShadowGroup
          attrName="image.decoration.boxShadow"
          grouped={false}
        />
        <FiltersGroup
          attrName="image.decoration.filter"
          grouped={false}
        />
      </GroupContainer>

      <GroupContainer id="hoverStyle" title={__('Hover Style', 'divi-optimaizer-modules')}>
        <FieldContainer
          attrName="hover.decoration.hoverClass"
          defaultAttr={defaultSettingsAttrs?.hover?.decoration?.hoverClass}
          label={__('Hover Style', 'divi-optimaizer-modules')}
          description={__('Input your value to action here.', 'divi-optimaizer-modules')}
          features={{
            sticky: false,
          }}
        >
          <SelectContainer
            options={{
              "select-hover": { label: 'Select Hover' },
              "holographic-horizontal": { label: 'Holographic Horizontal' },
              "holographic-vertical": { label: 'Holographic Vertical' },
              "ripple-effect": { label: 'Ripple Effect' },
              "effect-border-1": { label: 'Border Animation' },
              "effect-border-2": { label: 'Circular Reveal' },
              "blur-hover": { label: 'Blur Effect' },
              "glitch-effect": { label: 'Glitch Effect' },
              "effect-13": { label: 'Diagonal Sweep' },
              "kaleidoscope": { label: 'Kaleidoscope' },
              "liquid-distortion": { label: 'Liquid Distortion' },
              "neon-pulse": { label: 'Neon Pulse' },
              "magnetic-corner": { label: 'Magnetic Corner' },
              "time-portal": { label: 'Time Portal' },
              "spiral-zoom": { label: 'Spiral Zoom' },
              "split-color": { label: 'Split Colors' },
              "wave-distortion": { label: 'Wave Distortion' },
              "opacity-hover": { label: 'Opacity Effect' },
            }}

            grouped={false}
          />
        </FieldContainer>
        <FieldContainer
          attrName="hover.decoration.holographicHorizontal.transition"
          defaultAttr={defaultSettingsAttrs?.hover?.decoration?.holographicHorizontal?.transition}
          label={__('Transition Time', 'divi-optimaizer-modules')}
          description={__('Input your value to action here.', 'divi-optimaizer-modules')}
          features={{
            sticky: false,
          }}
          visible={holographicHorizontal === "holographic-horizontal"}
        >
          <RangeContainer min={500} max={10000} defaultUnit="ms" />
        </FieldContainer>

        {/*   -----------------------------------hologramVertical -------------------------------- */}
        <FieldContainer
          attrName="hover.decoration.holographicVertical.transition"
          defaultAttr={defaultSettingsAttrs?.hover?.decoration?.holographicVertical?.transition}
          label={__('Transition Time', 'divi-optimaizer-modules')}
          description={__('Input your value to action here.', 'divi-optimaizer-modules')}
          features={{
            sticky: false,
          }}
          visible={holographicVertical === "holographic-vertical"}
        >
          <RangeContainer min={500} max={10000} defaultUnit="ms" />
        </FieldContainer>

        {/*--------------------------------------- Ripple Effect ---------------------------------- */}
        <FieldContainer
          attrName="hover.decoration.rippleEffect.transition"
          defaultAttr={defaultSettingsAttrs?.hover?.decoration?.rippleEffect?.transition}
          label={__('Transition Time', 'divi-optimaizer-modules')}
          description={__('Input your value to action here.', 'divi-optimaizer-modules')}
          features={{
            sticky: false,
          }}
          visible={rippleEffect === "ripple-effect"}
        >
          <RangeContainer min={500} max={10000} defaultUnit="ms" />

        </FieldContainer>

        {/*------------------ Border Animation color -----------------------------*/}
        <FieldContainer
          attrName="hover.decoration.effectBorder1.topColor"
          defaultAttr={defaultSettingsAttrs?.hover?.decoration?.effectBorder1?.topColor}
          label={__('Border Color', 'divi-optimaizer-modules')}
          description={__('Input your value to action here.', 'divi-optimaizer-modules')}
          features={{
            sticky: false,
          }}
          visible={effectBorder1 === "effect-border-1"}
        >
          <ColorPickerContainer />
        </FieldContainer>
        <FieldContainer
          attrName="hover.decoration.effectBorder1.borderWidth"
          defaultAttr={defaultSettingsAttrs?.hover?.decoration?.effectBorder1?.borderWidth}
          label={__('Border width', 'divi-optimaizer-modules')}
          description={__('Input your value to action here.', 'divi-optimaizer-modules')}
          features={{
            sticky: false,
          }}
          visible={effectBorder1 === "effect-border-1"}
        >
          <RangeContainer min={1} max={500} />
        </FieldContainer>
        {/* ------------------ Circular Reveal color ----------------------------- */}
        <FieldContainer
          attrName="hover.decoration.effectBorder2.revealColor"
          defaultAttr={defaultSettingsAttrs?.hover?.decoration?.effectBorder2?.revealColor}
          label={__('Reveal Color', 'divi-optimaizer-modules')}
          description={__('Input your value to action here.', 'divi-optimaizer-modules')}
          features={{
            sticky: false,
          }}
          visible={effectBorder2 === "effect-border-2"}
        >
          <ColorPickerContainer />
        </FieldContainer>
        <FieldContainer
          attrName="hover.decoration.effectBorder2.positionTop"
          defaultAttr={defaultSettingsAttrs?.hover?.decoration?.effectBorder2?.positionTop}
          label={__('Reveal Position Top', 'divi-optimaizer-modules')}
          description={__('Input your value to action here.', 'divi-optimaizer-modules')}
          features={{
            sticky: false,
          }}
          visible={effectBorder2 === "effect-border-2"}
        >
          <RangeContainer min={0} max={100} defaultUnit="%" />
        </FieldContainer>
        <FieldContainer
          attrName="hover.decoration.effectBorder2.positionLeft"
          defaultAttr={defaultSettingsAttrs?.hover?.decoration?.effectBorder2?.positionLeft}
          label={__('Reveal Position Left', 'divi-optimaizer-modules')}
          description={__('Input your value to action here.', 'divi-optimaizer-modules')}
          features={{
            sticky: false,
          }}
          visible={effectBorder2 === "effect-border-2"}
        >
          <RangeContainer min={0} max={100} defaultUnit="%" />
        </FieldContainer>
        <FieldContainer
          attrName="hover.decoration.effectBorder2.revealRadius"
          defaultAttr={defaultSettingsAttrs?.hover?.decoration?.effectBorder2?.revealRadius}
          label={__('Reveal Radius', 'divi-optimaizer-modules')}
          description={__('Input your value to action here.', 'divi-optimaizer-modules')}
          features={{
            sticky: false,
          }}
          visible={effectBorder2 === "effect-border-2"}
        >
          <RangeContainer min={0} max={100} defaultUnit="%" />
        </FieldContainer>

        {/* ------------------ effect-13 ----------------------------- */}
        <FieldContainer
          attrName="hover.decoration.effect13.color"
          defaultAttr={defaultSettingsAttrs?.hover?.decoration?.effect13?.color}
          label={__('Color', 'divi-optimaizer-modules')}
          description={__('Input your value to action here.', 'divi-optimaizer-modules')}
          features={{
            sticky: false,
          }}
          visible={effectBorder2 === "effect-13"}
        >
          <ColorPickerContainer />
        </FieldContainer>

        {/* ------------------ magnetic-corner ----------------------------- */}
        <FieldContainer
          attrName="hover.decoration.magneticCorner.perspective"
          defaultAttr={defaultSettingsAttrs?.hover?.decoration?.magneticCorner?.perspective}
          label={__('Perspective Image', 'divi-optimaizer-modules')}
          description={__('Input your value to action here.', 'divi-optimaizer-modules')}
          features={{
            sticky: false,
          }}
          visible={magneticCorner === "magnetic-corner"}
        >
          <RangeContainer min={0} max={1500} defaultUnit="px" />
        </FieldContainer>
        <FieldContainer
          attrName="hover.decoration.magneticCorner.rotationX"
          defaultAttr={defaultSettingsAttrs?.hover?.decoration?.magneticCorner?.rotationX}
          label={__('Rotation X', 'divi-optimaizer-modules')}
          description={__('Input your value to action here.', 'divi-optimaizer-modules')}
          features={{
            sticky: false,
          }}
          visible={magneticCorner === "magnetic-corner"}
        >
          <RangeContainer min={-100} max={100} defaultUnit="deg" />
        </FieldContainer>
        <FieldContainer
          attrName="hover.decoration.magneticCorner.rotationY"
          defaultAttr={defaultSettingsAttrs?.hover?.decoration?.magneticCorner?.rotationY}
          label={__('Rotation X', 'divi-optimaizer-modules')}
          description={__('Input your value to action here.', 'divi-optimaizer-modules')}
          features={{
            sticky: false,
          }}
          visible={magneticCorner === "magnetic-corner"}
        >
          <RangeContainer min={-100} max={100} defaultUnit="deg" />
        </FieldContainer>

        {/* ------------------ Opacity ----------------------------- */}
        <FieldContainer
          attrName="hover.decoration.opacityColor"
          defaultAttr={defaultSettingsAttrs?.hover?.decoration?.opacityColor}
          label={__('Opacity Color', 'divi-optimaizer-modules')}
          description={__('Input your value to action here.', 'divi-optimaizer-modules')}
          features={{
            sticky: false,
          }}
          visible={opacity === "opacity-hover"}
        >
          <ColorPickerContainer />
        </FieldContainer>
      </GroupContainer>
      
      <SizingGroup />
      <SpacingGroup />
      <BorderGroup />
      <BoxShadowGroup />
      <FiltersGroup />
      <TransformGroup />
      <AnimationGroup />
    </React.Fragment>
  )

}
