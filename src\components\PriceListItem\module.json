{"name": "optimizer/dotm-price-list-item", "d4Shortcode": "", "title": "DOTM Price List Item", "titles": "DOTM Price List Items", "category": "child-module", "attributes": {"module": {"type": "object", "selector": "{{selector}}", "default": {"meta": {"adminLabel": {"desktop": {"value": "DOTM Price List Item"}}}}, "styleProps": {"border": {"important": true}}}, "contentContainer": {"type": "object", "selector": "{{selector}} .dotm_price_list_item_container"}, "image_container": {"type": "object", "selector": "{{selector}} .dotm_price_list_item_image_container", "default": {"decoration": {"spacing": {"desktop": {"value": {"margin": {"right": "25px"}}}}}}}, "image": {"type": "object", "selector": "{{selector}} .dotm_price_list_item_image", "default": {"advanced": {"img_width": {"desktop": {"value": "20%"}}, "alignment": {"desktop": {"value": "flex-start"}}}}, "styleProps": {"border": {"important": true}, "boxShadow": {"important": true}}, "tagName": "img", "attributes": {"class": "dotm_price_list_item_image"}, "elementType": "image", "childrenSanitizer": "et_core_esc_previously", "inlineEditor": "image"}, "title": {"type": "object", "selector": "{{selector}} .dotm_price_list_item_title", "default": {"decoration": {"font": {"font": {"desktop": {"value": {"size": "26px", "lineHeight": "1em", "weight": "600"}}}}}}, "styleProps": {"font": {"important": true}}, "defaultPrintedStyle": {"decoration": {"font": {"font": {"desktop": {"value": {"size": "26px", "lineHeight": "1em", "weight": "600"}}}}}}, "tagName": "div", "attributes": {"class": "dotm_price_list_item_title"}, "inlineEditor": "plainText", "elementType": "heading", "childrenSanitizer": "et_core_esc_previously"}, "price_tag": {"type": "object", "selector": "{{selector}} .dotm_price_list_item_price_tag", "default": {"decoration": {"font": {"font": {"desktop": {"value": {"size": "18px"}}}}}}, "defaultPrintedStyle": {"decoration": {"font": {"font": {"desktop": {"value": {"size": "18px"}}}}}}, "styleProps": {"font": {"important": true}}, "tagName": "div", "attributes": {"class": "dotm_price_list_item_price_tag"}}, "content": {"type": "object", "selector": "{{selector}} .dotm_price_list_item_description", "tagName": "div", "attributes": {"class": "dotm_price_list_item_description"}, "inlineEditor": "richText", "childrenSanitizer": "et_core_esc_previously"}, "separator": {"type": "object", "selector": "{{selector}} .dotm_price_list_item_separator", "default": {"decoration": {"style": {"desktop": {"value": "dashed"}}, "color": {"desktop": {"value": "#000000"}}, "width": {"desktop": {"value": "2px"}}, "gap_spacing": {"desktop": {"value": "10px"}}}}}, "icon": {"type": "object", "selector": "{{selector}} .dotm_price_list_item_icon"}}, "customCssFields": {"contentContainer": {"subName": "contentContainer", "selectorSuffix": " .dotm_price_list_item_container"}, "title": {"subName": "title", "selectorSuffix": " .dotm_price_list_item_title"}, "content": {"subName": "content", "selectorSuffix": " .example_child_module__content"}, "icon": {"subName": "icon", "selectorSuffix": " .example_child_module__icon.et-pb-icon"}}}