// External dependencies.
import React, { ReactElement, useState } from 'react';

// WordPress dependencies
import { __ } from '@wordpress/i18n';

// Divi dependencies.
import {
  AdminLabelGroup,
  BackgroundGroup,
  Field,
  FieldContainer,
  LinkGroup,
} from '@divi/module';
import {
  GroupContainer
} from '@divi/modal';
import {
  TextContainer,
} from '@divi/field-library';
import {
  type Module
} from '@divi/types';
import { MultiHeadingAttrs } from "./types";


export const SettingsContent = ({
  defaultSettingsAttrs,
}: Module.Settings.Panel.Props<MultiHeadingAttrs>): ReactElement => {

  const [options, setOptions] = useState();
  const [hideShow, setHideShow] = useState(false);


  const handleSelects = (e: any) => {
    setOptions(e.target.value);
  };
  return (
    <React.Fragment>
      <GroupContainer
        id="mainContent"
        title={__('Heading Text', 'optimaizer-modules')}
      >
        <FieldContainer
          attrName="title1.innerContent"
          label={__('First Heading', 'optimaizer-modules')}
          description={__('Input your value to action title here.', 'optimaizer-modules')}
          features={{
            sticky: false,
          }}
        >
          <TextContainer />
        </FieldContainer>
        <FieldContainer
          attrName="title2.innerContent"
          label={__('Second Heading', 'optimaizer-modules')}
          description={__('Input your value to action title here.', 'optimaizer-modules')}
          features={{
            sticky: false,
          }}
        >
          <TextContainer />
        </FieldContainer>
        <FieldContainer
          attrName="title3.innerContent"
          label={__('Third Heading', 'optimaizer-modules')}
          description={__('Input your value to action title here.', 'optimaizer-modules')}
          features={{
            sticky: false,
          }}
        >
          <TextContainer />
        </FieldContainer>
      </GroupContainer>
      <LinkGroup />
      <BackgroundGroup
        
      />
      <AdminLabelGroup />
    </React.Fragment>
  );
};

