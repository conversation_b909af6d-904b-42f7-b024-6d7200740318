{"name": "dotm/optimize-rating", "d4Shortcode": "", "title": "Optimize Rating", "titles": "Optimize Ratings", "moduleIcon": "dotm/optimize-rating", "category": "module", "attributes": {"module": {"type": "object", "selector": "{{selector}}", "default": {"meta": {"adminLabel": {"desktop": {"value": "Optimize Rating"}}}, "decoration": {"sizing": {"desktop": {"value": {"height": "40px"}}}}, "advanced": {"text": {"text": {"desktop": {"value": {"color": "light"}}}}}}, "defaultPrintedStyle": {"decoration": {"background": {"desktop": {"value": {"color": "#ecf4f7"}}}}}, "styleProps": {"border": {"important": true}, "spacing": {"important": true}}}, "tooltip": {"type": "object", "selector": "{{selector}} .dotm_optimize_rating_tooltip", "default": {"decoration": {"font": {"font": {"desktop": {"value": {"size": "12px", "color": "#ffffff"}}}}, "tooltip_BGcolor": {"desktop": {"value": "#000000b3"}}, "spacing": {"desktop": {"value": {"padding": {"right": "10px", "left": "10px", "top": "15px", "bottom": "15px"}, "margin": {"bottom": "10px"}}}}, "border": {"desktop": {"value": {"radius": {"topLeft": "4px", "topRight": "4px", "bottomLeft": "4px", "bottomRight": "4px"}}}}, "vr_position": {"desktop": {"value": "0%"}}, "hr_position": {"desktop": {"value": "35%"}}, "use_tooltip": {"desktop": {"value": "off"}}}}}, "rating": {"type": "object", "selector": "{{selector}} .rating", "default": {"advanced": {"rating_range": {"desktop": {"value": "5"}}, "star_active_color": {"desktop": {"value": "#ffb300"}}, "star_color": {"desktop": {"value": "#ddd"}}, "star_size": {"desktop": {"value": "35px"}}, "star_gap": {"desktop": {"value": "5px"}}, "icon_type": {"desktop": {"value": "filled"}}, "alignment": {"desktop": {"value": "flex-start"}}}}}, "title": {"type": "object", "selector": "{{selector}} .dotm_optimize_rating_title", "default": {"decoration": {"spacing": {"desktop": {"value": {"margin": {"right": "10px"}}}}}}, "defaultPrintedStyle": {"decoration": {"font": {"font": {"desktop": {"value": {"size": "18px", "lineHeight": "1em", "weight": "500"}}}}}}, "styleProps": {"font": {"important": {"font": {"desktop": {"value": {"color": true}}}}}}, "tagName": "span", "attributes": {"class": "dotm_optimize_rating_title"}, "inlineEditor": "plainText", "elementType": "heading", "childrenSanitizer": "et_core_esc_previously"}}, "customCssFields": {"contentContainer": {"subName": "contentContainer", "selectorSuffix": " .example_static_module__content-container"}, "title": {"subName": "title", "selector": "div{{selector}}", "selectorSuffix": " .example_static_module__title"}, "content": {"subName": "content", "selectorSuffix": " .example_static_module__content"}, "image": {"subName": "image", "selectorSuffix": " .example_static_module__image img"}}}