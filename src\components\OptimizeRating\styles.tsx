// External dependencies.
import React, { ReactElement } from 'react';

// Divi dependencies.
import {
  StyleContainer,
  StylesProps,
  CssStyle,
  TextStyle,
} from '@divi/module';

// Local dependencies.
import { OptimizeRatingAttrs } from './types';
import { cssFields } from './custom-css';

/**
 * OptimizeRating's style components.
 *
 * @since ??
 */
export const ModuleStyles = ({
  attrs,
  elements,
  settings,
  orderClass,
  mode,
  state,
  noStyleTag,
}: StylesProps<OptimizeRatingAttrs>): ReactElement => {
  const containerSelector = `${orderClass} .dotm_optimize_rating_container`;
  const filledStarsSelector = `${orderClass} .dotm_optimize_rating_star.dotm_optimize_rating_star_filled`;
  const starsSelector = `${orderClass} .dotm_optimize_rating_stars`;
  const tooltipSelector = `${orderClass} .dotm_optimize_rating_tooltip`;
  const hoverTooltipSelector = `${orderClass} .dotm_optimize_rating_stars:hover .dotm_optimize_rating_tooltip`;
  const icon_type = attrs?.rating?.advanced?.icon_type?.desktop?.value ?? '';
  const alignment = attrs?.rating?.advanced?.alignment?.desktop?.value ?? '';
  const hr_position = attrs?.tooltip?.decoration?.hr_position?.desktop?.value ?? '';
   const use_tooltip = attrs?.tooltip?.decoration?.use_tooltip?.desktop?.value ?? ''
   const content_reverse = attrs?.rating?.decoration?.content_reverse?.desktop?.value ?? 'off'

  return (
    <StyleContainer mode={mode} state={state} noStyleTag={noStyleTag}>
      {/* Module */}
      {elements.style({
        attrName: 'module',
        styleProps: {
          disabledOn: {
            disabledModuleVisibility: settings?.disabledModuleVisibility,
          },
          advancedStyles: [
            {
              componentName: "divi/common",
              props: {
                selector: filledStarsSelector,
                attr: attrs?.rating?.advanced?.star_active_color,
                property: "color"
              }
            },
            {
              componentName: "divi/common",
              props: {
                selector: starsSelector,
                attr: attrs?.rating?.advanced?.star_color,
                property: "color"
              }
            },
            {
              componentName: "divi/common",
              props: {
                selector: starsSelector,
                attr: attrs?.rating?.advanced?.star_size,
                property: "font-size"
              }
            },
            {
              componentName: "divi/common",
              props: {
                selector: starsSelector,
                attr: attrs?.rating?.advanced?.star_gap,
                property: "letter-spacing"
              }
            },
            {
              componentName: "divi/common",
              props: {
                selector: starsSelector,
                attr: attrs?.rating?.advanced?.icon_type,
                property: icon_type === "normal" ? "font-weight: 900;" : ''
              }
            },
            {
              componentName: "divi/common",
              props: {
                selector: filledStarsSelector,
                attr: attrs?.rating?.advanced?.icon_type,
                property: icon_type === "filled" ? "font-weight: 900;" : ''
              }
            },
            {
              componentName: "divi/common",
              props: {
                selector: containerSelector,
                attr: attrs?.rating?.advanced?.alignment,
                property: "justify-content"
              }
            },
            {
              componentName: "divi/common",
              props: {
                selector: containerSelector,
                attr: attrs?.rating?.decoration?.content_reverse,
                property: content_reverse === "on" ? "flex-direction: row-reverse;" : "flex-direction: row;"
              }
            },
          ]
        },
      })}
      {/* tooltip */}
      {elements.style({
        attrName: 'tooltip',
        styleProps: {
          advancedStyles: [
            {
              componentName: "divi/common",
              props: {
                selector: tooltipSelector,
                attr: attrs?.tooltip?.decoration?.tooltip_BGcolor,
                property: "background-color"
              }
            },
            {
              componentName: "divi/common",
              props: {
                selector: tooltipSelector,
                attr: attrs?.tooltip?.decoration?.hr_position,
                property: `left: ${hr_position};`
              }
            },
            {
              componentName: "divi/common",
              props: {
                selector: hoverTooltipSelector,
                attr: attrs?.tooltip?.decoration?.use_tooltip,
                property: use_tooltip === "on" ? "opacity: 1; visibility: visible;" : '',
              }
            },
          ]
        }
      })}

      {/* Title */}
      {elements.style({
        attrName: 'title',
      })}

      {/*
       * We need to add CssStyle at the very bottom of other components
       * so that custom css can override module styles till we find a
       * more elegant solution.
       */}
      <CssStyle
        selector={orderClass}
        attr={attrs.css}
        cssFields={cssFields}
      />

    </StyleContainer>
  );
};
