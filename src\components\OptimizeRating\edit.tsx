// External Dependencies.
import React, { ReactElement, useEffect } from 'react';
import classnames from 'classnames';

// Divi Dependencies.
import { ModuleContainer } from '@divi/module';

// Local Dependencies.
import { OptimizeRatingEditProps } from './types';
import { ModuleStyles } from './styles';
import { moduleClassnames } from './module-classnames';
import { ModuleScriptData } from './module-script-data';

import { rating } from './script/rating.js';
import { use } from '@divi/data';

/**
 * OptimizeRating edit component of visual builder.
 *
 * @since ??
 *
 * @param {OptimizeRatingEditProps} props React component props.
 *
 * @returns {ReactElement}
 */
export const OptimizeRatingEdit = (props: OptimizeRatingEditProps): ReactElement => {
  const {
    attrs,
    elements,
    id,
    name,
  } = props;

  const [uuid] = use(() => {
    return [Math.random().toString(36).substring(2, 10) + (new Date()).getTime().toString(36)];
  }, []);

  console.log(attrs);

  // Get attributes.
  const use_tooltip = attrs?.tooltip?.decoration?.use_tooltip?.desktop?.value ?? 'off'
  const maxRating = attrs?.rating?.advanced?.rating_range?.desktop?.value ?? ''
  const currentRating = Number(attrs?.rating?.advanced?.rating?.desktop?.value) ?? ''
  const title = attrs?.title?.innerContent?.desktop?.value ?? ''
 

// console.log(typeof currentRating, 'currentRating')

  useEffect(() => {
    rating({
      maxRating,
      currentRating,
      containerId: uuid,
      use_tooltip,
    });
  }, [maxRating, currentRating, use_tooltip, uuid]);

  return (
    <ModuleContainer
      attrs={attrs}
      elements={elements}
      id={id}
      name={name}
      stylesComponent={ModuleStyles}
      classnamesFunction={moduleClassnames}
      scriptDataComponent={ModuleScriptData}
      key={title}
    >
      {elements.styleComponents({
        attrName: 'module',
      })}
      <div className="dotm_optimize_rating_container" id={uuid}>
        {elements.render({
          attrName:'title',
        })}
        <div className="dotm_optimize_rating_stars" />
      </div>
    </ModuleContainer>
  );
};
