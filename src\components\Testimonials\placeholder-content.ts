// Divi dependencies.
import {placeholderContent as placeholder} from '@divi/module';


// Local dependencies.
import {TestimonialsAttrs} from './types';
import { getPlaceholderTitle, getPlaceholderBody, getPlaceholderImage } from '../../utils/placeholder-fallbacks';

export const placeholderContent: TestimonialsAttrs = {
  name: {
    innerContent: {
      desktop: {
         value: getPlaceholderTitle(placeholder, 'Md Nayon Ali'),
      },
    }
  },
  content: {
    innerContent: {
      desktop: {
        value: getPlaceholderBody(placeholder, 'Hi everyone, Divi Optimizer has 55+ modules that come in handy when you want to create a responsive WordPress Website.'),
      },
    }
  },
  label: {
    innerContent: {
      desktop: {
         value: getPlaceholderTitle(placeholder, 'Frontend Developer'),
      },
    }
  },
  image: {
    innerContent: {
      desktop: {
        value: {
          src: getPlaceholderImage(placeholder, 'landscape'),
        },
      },
    },
  },
  
};
