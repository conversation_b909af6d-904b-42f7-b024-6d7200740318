import { omit } from 'lodash';

import { addAction } from '@wordpress/hooks';

import { registerModule } from '@divi/module-library';

import './module-icons';

import { BeforeAfterSlider } from './components/BeforeAfterSlider';
import { BlockRevealText } from './components/BlockRevealText';
import { Breadcrumbs } from './components/Breadcrumbs';
import { BreadcrumbItem } from './components/BreadcrumbsItem';
import { BusinessHour } from './components/BusinessHour';
import { BusinessHourItem } from './components/BusinessHourItem';
import { ContentToggle } from './components/ContentToggle';
import { FacebookLikeButton } from './components/FacebookLikeButton';
import { FacebookPost } from './components/FacebookPost';
import { FbPage } from './components/FbPage';
import { FbVideoModule } from './components/FbVideo';
import { GlitchText } from './components/GlitchText';
import { GoogleMap } from './components/GoogleMap';
import { ImageAccordion } from './components/ImageAccordion';
import { ImageAccordionItem } from './components/ImageAccordionItem';
import { ImageHover } from './components/ImageHover';
import { ImageLogoCarousel } from './components/ImageLogoCarousel';
import { ImageMagnifier } from './components/ImageMagnifier';
import { MasonaryGellary } from './components/MasonaryGellary';
import { MasonaryModule } from './components/Masonary';
import { MaskText } from './components/MaskText';
import { MotionTextColor } from './components/MotionTextColor';
import { MultiHeading } from './components/multi-heading';
import { OptimizerCounter } from './components/OptimizerCounter';
import { OptimizerShapes } from './components/OptimizerShapes';
import { OptimizeCard } from './components/OptimizeCard';
import { OptimizeCardCarousel } from './components/OptimizeCardCarousel';
import { OptimizeCardCarouselItem } from './components/OptimizeCardCarouselItem';
import { OptimizeCountDown } from './components/OptimizeCountDown';
import { OptimizeFAQ } from './components/OptimizeFAQ';
import { OptimizeFAQItem } from './components/OptimizeFAQItem';
import { OptimizeFlipBox } from './components/OptimizeFlipBox';
import { OptimizeIconItem } from './components/OptimizeIconItem';
import { OptimizeIconList } from './components/OptimizeIconList';
import { OptimizeImageReveal } from './components/OptimizeImageReveal';
import { OptimizeImageTilt } from './components/OptimizeImageTilt';
import { OptimizeRating } from './components/OptimizeRating';
import { OptimizeTextContent } from './components/OptimizeTextContent';
import { OptimizeTimeline } from './components/OptimizeTimeline';
import { OptimizeTimelineItem } from './components/OptimizeTimelineItem';
import { Pdfviewer } from './components/PdfViewer';
import { PriceList } from './components/PriceList';
import { PriceListItem } from './components/PriceListItem';
import { ProfileOptimaizer } from './components/ProfileOptimaizer';
import { ProfileOptimazerItem } from './components/ProfileOptimazerItem';
import { ProgressBar } from './components/ProgressBar';
import { ShuffleLetter } from './components/ShuffleLetter';
import { SocialShareButtons } from './components/SocialShareButtons';
import { SocialShareButtonItem } from './components/SocialShareButtonItem';
import { StepFlow } from './components/StepFlow';
import { TextBadge } from './components/TextBadge';
import { TextDivider } from './components/TextDivider';
import { TextGradient } from './components/TextGradient';
import { TextNotation } from './components/TextNotation';
import { TextScroll } from './components/TextScroll';
import { TwitterButton } from './components/TwitterButton';
import { TwitterFollowButton } from './components/TwitterFollowButton';
import { OptimizeButtons } from './components/OptimizeButtons';
import { OptimizeButtonItem } from './components/OptimizeButtonItem';
import { TwitterTimeline } from './components/TwitterTimeline';
import { TypingEffect } from './components/typing-effect';
import { Testimonials } from './components/Testimonials';
import { OptimizePricingTables } from './components/OptimizePricingTables';
import { OptimizePricingTablesItem } from './components/OptimizePricingTablesItem';



// Register modules.
addAction('divi.moduleLibrary.registerModuleLibraryStore.after', 'extensionExample', () => {
  registerModule(BeforeAfterSlider.metadata, omit(BeforeAfterSlider, 'metadata'));
  registerModule(BlockRevealText.metadata, omit(BlockRevealText,'metadata'));
  registerModule(Breadcrumbs.metadata, omit(Breadcrumbs, 'metadata'));
  registerModule(BreadcrumbItem.metadata, omit(BreadcrumbItem, 'metadata'));
  registerModule(BusinessHour.metadata, omit(BusinessHour, 'metadata'));
  registerModule(BusinessHourItem.metadata, omit(BusinessHourItem, 'metadata'));
  registerModule(ContentToggle.metadata, omit(ContentToggle, 'metadata'));
  registerModule(FacebookLikeButton.metadata, omit(FacebookLikeButton,'metadata'));
  registerModule(FacebookPost.metadata, omit(FacebookPost, 'metadata'));
  registerModule(FbPage.metadata, omit(FbPage, 'metadata'));
  registerModule(FbVideoModule.metadata, omit(FbVideoModule, 'metadata'));
  registerModule(GlitchText.metadata, omit(GlitchText,'metadata'));
  registerModule(GoogleMap.metadata, omit(GoogleMap, 'metadata'));
  registerModule(ImageAccordion.metadata, omit(ImageAccordion, 'metadata'));
  registerModule(ImageAccordionItem.metadata, omit(ImageAccordionItem, 'metadata'));
  registerModule(ImageHover.metadata, omit(ImageHover, 'metadata'));
  registerModule(ImageLogoCarousel.metadata, omit(ImageLogoCarousel, 'metadata'));
  registerModule(ImageMagnifier.metadata, omit(ImageMagnifier,'metadata'));
  registerModule(MasonaryGellary.metadata, omit(MasonaryGellary,'metadata'));
  registerModule(MasonaryModule.metadata, omit(MasonaryModule, 'metadata'));
  registerModule(MaskText.metadata, omit(MaskText,'metadata'));
  registerModule(MotionTextColor.metadata, omit(MotionTextColor,'metadata'));
  registerModule(MultiHeading.metadata, omit(MultiHeading, 'metadata'));
  registerModule(OptimizerCounter.metadata, omit(OptimizerCounter,'metadata'));
  registerModule(OptimizerShapes.metadata, omit(OptimizerShapes, 'metadata'));
  registerModule(OptimizeCard.metadata, omit(OptimizeCard, 'metadata'));
  registerModule(OptimizeCardCarousel.metadata, omit(OptimizeCardCarousel,'metadata'));
  registerModule(OptimizeCardCarouselItem.metadata, omit(OptimizeCardCarouselItem,'metadata'));
  registerModule(OptimizeCountDown.metadata, omit(OptimizeCountDown,'metadata'));
  registerModule(OptimizeFAQ.metadata, omit(OptimizeFAQ, 'metadata'));
  registerModule(OptimizeFAQItem.metadata, omit(OptimizeFAQItem, 'metadata'));
  registerModule(OptimizeFlipBox.metadata, omit(OptimizeFlipBox, 'metadata'));
  registerModule(OptimizeIconItem.metadata, omit(OptimizeIconItem,'metadata'));
  registerModule(OptimizeIconList.metadata, omit(OptimizeIconList,'metadata'));
  registerModule(OptimizeImageReveal.metadata, omit(OptimizeImageReveal,'metadata'));
  registerModule(OptimizeImageTilt.metadata, omit(OptimizeImageTilt, 'metadata'));
  registerModule(OptimizeRating.metadata, omit(OptimizeRating,'metadata'));
  registerModule(OptimizeTextContent.metadata, omit(OptimizeTextContent,'metadata'));
  registerModule(OptimizeTimeline.metadata, omit(OptimizeTimeline,'metadata'));
  registerModule(OptimizeTimelineItem.metadata, omit(OptimizeTimelineItem,'metadata'));
  registerModule(Pdfviewer.metadata, omit(Pdfviewer, 'metadata'));
  registerModule(PriceList.metadata, omit(PriceList, 'metadata'));
  registerModule(PriceListItem.metadata, omit(PriceListItem, 'metadata'));
  registerModule(ProfileOptimaizer.metadata, omit(ProfileOptimaizer, 'metadata'));
  registerModule(ProfileOptimazerItem.metadata, omit(ProfileOptimazerItem, 'metadata'));
  registerModule(ProgressBar.metadata, omit(ProgressBar, 'metadata'));
  registerModule(ShuffleLetter.metadata, omit(ShuffleLetter,'metadata'));
  registerModule(SocialShareButtons.metadata, omit(SocialShareButtons,'metadata'));
  registerModule(SocialShareButtonItem.metadata, omit(SocialShareButtonItem,'metadata'));
  registerModule(StepFlow.metadata, omit(StepFlow, 'metadata'));
  registerModule(TextBadge.metadata, omit(TextBadge,'metadata'));
  registerModule(TextDivider.metadata, omit(TextDivider,'metadata'));
  registerModule(TextGradient.metadata, omit(TextGradient, 'metadata'));
  registerModule(TextNotation.metadata, omit(TextNotation,'metadata'));
  registerModule(TextScroll.metadata, omit(TextScroll, 'metadata'));
  registerModule(TwitterButton.metadata, omit(TwitterButton,'metadata'));
  registerModule(TwitterFollowButton.metadata, omit(TwitterFollowButton,'metadata'));
  registerModule(OptimizeButtons.metadata, omit(OptimizeButtons,'metadata'));
  registerModule(OptimizeButtonItem.metadata, omit(OptimizeButtonItem,'metadata'));

  registerModule(TwitterTimeline.metadata, omit(TwitterTimeline,'metadata'));
  registerModule(TypingEffect.metadata, omit(TypingEffect, 'metadata'));
  registerModule(Testimonials.metadata, omit(Testimonials, 'metadata'));
  registerModule(OptimizePricingTables.metadata, omit(OptimizePricingTables, 'metadata'));
  registerModule(OptimizePricingTablesItem.metadata, omit(OptimizePricingTablesItem, 'metadata'));
});
