{"name": "dotm/optimize-text-content", "d4Shortcode": "", "title": "Optimize Text Content", "titles": "Optimize Text Contents", "moduleIcon": "dotm/optimize-text-content", "category": "module", "attributes": {"module": {"type": "object", "selector": "{{selector}}", "default": {"meta": {"adminLabel": {"desktop": {"value": "Optimize Text Content"}}}, "advanced": {"text": {"text": {"desktop": {"value": {"color": "light"}}}}}}, "defaultPrintedStyle": {"decoration": {"background": {"desktop": {"value": {"color": "#ecf4f7"}}}}}, "styleProps": {"border": {"important": true}, "spacing": {"important": true}}}, "content": {"type": "object", "selector": "{{selector}} .dotm_optimize_text_content_text", "tagName": "div", "attributes": {"class": "dotm_optimize_text_content_text"}, "inlineEditor": "richText", "childrenSanitizer": "et_core_esc_previously", "styleProps": {"bodyFont": {"selectors": {"desktop": {"value": "{{selector}} .dotm_optimize_text_content_text"}}}}}}, "customCssFields": {"contentContainer": {"subName": "contentContainer", "selectorSuffix": " .example_static_module__content-container"}, "title": {"subName": "title", "selector": "div{{selector}}", "selectorSuffix": " .example_static_module__title"}, "content": {"subName": "content", "selectorSuffix": " .dotm_optimize_text_content_text"}, "image": {"subName": "image", "selectorSuffix": " .example_static_module__image img"}}}