// External dependencies.
import React, { ReactElement } from 'react';

// Divi dependencies.
import {
  StyleContainer,
  StylesProps,
  CssStyle,
  TextStyle,
} from '@divi/module';

// Local dependencies.
import { ImageHoverAttrs } from './types';
import { cssFields } from './custom-css';

/**
 * Static Module's style components.
 *
 * @since ??
 */
export const ModuleStyles = ({
  attrs,
  elements,
  settings,
  orderClass,
  mode,
  state,
  noStyleTag,
}: StylesProps<ImageHoverAttrs>): ReactElement => {
  const textSelector = `${orderClass} .dotm__content-container`;
  const imageWraperSelector = `${orderClass} .dotm_module__image`;
  const imageSize = `${orderClass} .dotm_module__image img`;
  const holographicSelector = `${orderClass} .holographic-horizontal::before`;
  const holographicVerticalSelector = `${orderClass} .holographic-vertical::before`;
  const rippleEffectSelector = `${orderClass} .ripple-effect::before`;
  const effectBorder1BeforeSelector = `${orderClass} .effect-border-1::before`;
  const effectBorder1AfterSelector = `${orderClass} .effect-border-1::after`;
  const effectBorder2BeforeSelector = `${orderClass} .effect-border-2::before`;
  const effect13ColorSelector = `${orderClass} .effect-13::after`;
  const effect14ColorSelector = `${orderClass} .magnetic-corner:hover img`;
  const overlaySelector = `${orderClass} .opacity-hover`;


  const borderColor = attrs?.hover?.decoration?.effectBorder1?.topColor?.desktop?.value ?? '';
  const borderWidth = attrs?.hover?.decoration?.effectBorder1?.borderWidth?.desktop?.value ?? '';

  // Reveal Color
  const revealColor = attrs?.hover?.decoration?.effectBorder2?.revealColor?.desktop?.value ?? '';
  const revealPositionTop = attrs?.hover?.decoration?.effectBorder2?.positionTop?.desktop?.value ?? '';
  const revealPositionLeft = attrs?.hover?.decoration?.effectBorder2?.positionLeft?.desktop?.value ?? '';
  const revealRadius = attrs?.hover?.decoration?.effectBorder2?.revealRadius?.desktop?.value ?? '';

  // Magnetic Corner
  const magneticCorner = attrs?.hover?.decoration?.magneticCorner?.perspective?.desktop?.value ?? '';
  const magneticCornerRotateX = attrs?.hover?.decoration?.magneticCorner?.rotationX?.desktop?.value ?? '';
  const magneticCornerRotateY = attrs?.hover?.decoration?.magneticCorner?.rotationY?.desktop?.value ?? '';


  return (
    <StyleContainer mode={mode} state={state} noStyleTag={noStyleTag}>
      {/* Module */}
      {elements.style({
        attrName: 'module',
        styleProps: {
          disabledOn: {
            disabledModuleVisibility: settings?.disabledModuleVisibility,
          },
          advancedStyles: [
            {
              componentName: "divi/text",
              props: {
                selector: textSelector,
                attr: attrs?.module?.advanced?.text,
              }
            },
            {
              componentName: "divi/common",
              props: {
                selector: imageWraperSelector,
                attr: attrs?.image?.decoration?.imagewidth,
                property: 'width',
              }
            },
            {
              componentName: "divi/common",
              props: {
                selector: imageWraperSelector,
                attr: attrs?.image?.decoration?.imageHeight,
                property: 'height',
              }
            },
            {
              componentName: "divi/common",
              props: {
                selector: imageSize,
                attr: attrs?.image?.decoration?.imageSize,
                property: 'object-fit',
              }
            },
            {
              componentName: "divi/common",
              props: {
                selector: holographicSelector,
                attr: attrs?.hover?.decoration?.holographicHorizontal?.transition,
                property: 'transition',
              }
            },
            {
              componentName: "divi/common",
              props: {
                selector: holographicVerticalSelector,
                attr: attrs?.hover?.decoration?.holographicVertical?.transition,
                property: 'transition',
              }
            },
            {
              componentName: "divi/common",
              props: {
                selector: rippleEffectSelector,
                attr: attrs?.hover?.decoration?.rippleEffect?.transition,
                property: 'transition',
              }
            },
            {
              componentName: "divi/common",
              props: {
                selector: effectBorder1BeforeSelector,
                attr: attrs?.hover?.decoration?.effectBorder1?.topColor,
                property: `border-top:${borderWidth} solid ${borderColor}; border-left:${borderWidth} solid ${borderColor};`,
              }
            },
            {
              componentName: "divi/common",
              props: {
                selector: effectBorder1AfterSelector,
                attr: attrs?.hover?.decoration?.effectBorder1?.topColor,
                property: `border-bottom:${borderWidth} solid ${borderColor}; border-right:${borderWidth} solid ${borderColor};`,
              }
            },
            {
              componentName: "divi/common",
              props: {
                selector: effectBorder2BeforeSelector,
                attr: attrs?.hover?.decoration?.effectBorder2?.revealColor,
                property: `top:${revealPositionTop}; left:${revealPositionLeft}; background:${revealColor}; border-radius:${revealRadius};`,
              }
            },
            {
              componentName: "divi/common",
              props: {
                selector: effect13ColorSelector,
                attr: attrs?.hover?.decoration?.effect13?.color,
                property: "background",
              }
            },
            {
              componentName: "divi/common",
              props: {
                selector: effect14ColorSelector,
                attr: attrs?.hover?.decoration?.magneticCorner?.perspective,
                property: `transform: perspective(${magneticCorner}) rotateX(${magneticCornerRotateX}) rotateY(${magneticCornerRotateY});`,
              }
            },
            {
              componentName: "divi/common",
              props: {
                selector: overlaySelector,
                attr: attrs?.hover?.decoration?.opacityColor,
                property: 'background',
              }
            }

          ]
        },
      })}
      {/* Image */}
      {elements.style({
        attrName: 'image',
      })}
      {
        elements.style({
          attrName: 'hover',
        })}
      {/*
       * We need to add CssStyle at the very bottom of other components
       * so that custom css can override module styles till we find a
       * more elegant solution.
       */}
      <CssStyle
        selector={orderClass}
        attr={attrs.css}
        cssFields={cssFields}
      />

    </StyleContainer>
  );
};
