{"name": "dotm/image-hover", "d4Shortcode": "", "title": "Image Hover", "titles": "", "moduleIcon": "dotm/image-hover", "category": "module", "attributes": {"module": {"type": "object", "selector": "{{selector}}", "default": {"meta": {"adminLabel": {"desktop": {"value": "Image Hover"}}}, "advanced": {"text": {"text": {"desktop": {"value": {"color": "light"}}}}}}, "defaultPrintedStyle": {"decoration": {"background": {"desktop": {"value": {"color": "#ecf4f7"}}}}}, "styleProps": {"border": {"important": true}}}, "image": {"type": "object", "selector": "{{selector}} .dotm_module__image", "styleProps": {"border": {"important": true}}, "default": {"decoration": {"imagewidth": {"desktop": {"value": "100%"}}, "imageHeight": {"desktop": {"value": "100%"}}, "imageSize": {"desktop": {"value": "cover"}}}}}, "hover": {"type": "object", "default": {"decoration": {"hoverClass": {"desktop": {"value": "select-hover"}}, "holographicHorizontal": {"transition": {"desktop": {"value": "500ms"}}}, "holographicVertical": {"transition": {"desktop": {"value": "500ms"}}}, "rippleEffect": {"transition": {"desktop": {"value": "500ms"}}}, "effectBorder1": {"topColor": {"desktop": {"value": "#fff"}}}, "effectBorder2": {"revealColor": {"desktop": {"value": "rgb(223 128 128 / 20%)"}}, "positionTop": {"desktop": {"value": "50%"}}, "positionLeft": {"desktop": {"value": "50%"}}, "revealRadius": {"desktop": {"value": "50%"}}}, "effect13": {"color": {"desktop": {"value": "rgba(131,0,233,0.25)"}}}, "magneticCorner": {"perspective": {"desktop": {"value": "1000px"}}, "rotationX": {"desktop": {"value": "10deg"}}, "rotationY": {"desktop": {"value": "10deg"}}}}}}}, "customCssFields": {"contentContainer": {"subName": "contentContainer", "selectorSuffix": " .dotm_module__content-container"}, "image": {"subName": "image", "selectorSuffix": " .dotm_module__image img"}}}