// External dependencies.
import React, { ReactElement } from 'react';

// WordPress dependencies
import { __ } from '@wordpress/i18n';

// Divi dependencies.
import {
  AdminLabelGroup,
  BackgroundGroup,
  FieldContainer,
  LinkGroup,
} from '@divi/module';
import {
  GroupContainer
} from '@divi/modal';
import {
  IconPickerContainer,
  RichTextContainer,
  SelectContainer,
  TextContainer,
  ToggleContainer,
  UploadContainer,
} from '@divi/field-library';
import {
  type Module,
} from '@divi/types';


import { OptimizeRatingAttrs } from "./types";

export const SettingsContent = ({
  defaultSettingsAttrs,
}: Module.Settings.Panel.Props<OptimizeRatingAttrs>): ReactElement => (
  <React.Fragment>
    <GroupContainer
      id="rating"
      title={__('Rating', 'divi-optimaizer-modules')}
    >
      <FieldContainer
        attrName="rating.advanced.icon_type"
        label={__('Rating Icon Type', 'divi-optimaizer-modules')}
        description={__('Input your value to action here.', 'divi-optimaizer-modules')}
        defaultAttr={defaultSettingsAttrs?.rating?.advanced?.icon_type}
        features={{
          sticky: false,
        }}
      >
        <SelectContainer
          options={{
            "normal": { label: "Normal Icon" },
            "filled": { label: "filled Icon" },
          }}
        />
      </FieldContainer>

      <FieldContainer
        attrName="rating.advanced.rating_range"
        label={__('Rating Range', 'divi-optimaizer-modules')}
        description={__('Input your value to action here.', 'divi-optimaizer-modules')}
        defaultAttr={defaultSettingsAttrs?.rating?.advanced?.rating_range}
        features={{
          sticky: false,
        }}
      >
        <SelectContainer
          options={{
            "5": { label: "0-5" },
            "10": { label: "0-10" },
          }}
        />
      </FieldContainer>

      <FieldContainer
        attrName="rating.advanced.rating"
        label={__('Rating', 'divi-optimaizer-modules')}
        description={__('Input your value to action here.', 'divi-optimaizer-modules')}
        features={{
          sticky: false,
        }}
      >
        <TextContainer />
      </FieldContainer>

      <FieldContainer
        attrName="title.innerContent"
        label={__('Label', 'divi-optimaizer-modules')}
        description={__('Input your value to action here.', 'divi-optimaizer-modules')}
        features={{
          sticky: false,
        }}
      >
        <TextContainer />
      </FieldContainer>

      <FieldContainer
        attrName="tooltip.decoration.use_tooltip"
        label={__('Use Tooltip', 'divi-optimaizer-modules')}
        description={__('Input your value to action here.', 'divi-optimaizer-modules')}
        defaultAttr={defaultSettingsAttrs?.tooltip?.decoration?.use_tooltip}
        features={{
          sticky: false,
        }}
      >
        <ToggleContainer />
      </FieldContainer>
      <FieldContainer
        attrName="rating.decoration.content_reverse"
        label={__('Content Reverse', 'divi-optimaizer-modules')}
        description={__('Input your value to action here.', 'divi-optimaizer-modules')}
        defaultAttr={defaultSettingsAttrs?.rating?.decoration?.content_reverse}
        features={{
          sticky: false,
        }}
      >
        <ToggleContainer />
      </FieldContainer>
    </GroupContainer>
    <LinkGroup />
    <BackgroundGroup
      defaultGroupAttr={defaultSettingsAttrs?.module?.decoration?.background?.asMutable({ deep: true }) ?? {}}
    />
    <AdminLabelGroup />
  </React.Fragment>
);
