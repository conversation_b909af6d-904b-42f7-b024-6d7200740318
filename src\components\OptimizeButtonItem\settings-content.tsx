// External dependencies.
import React, { ReactElement } from 'react';

// WordPress dependencies
import { __ } from '@wordpress/i18n';

// Divi dependencies.
import {
  BackgroundGroup,
  FieldContainer,
  LinkGroup,
} from '@divi/module';
import { GroupContainer } from '@divi/modal';
import {
  IconPickerContainer,
  RichTextContainer,
  TextContainer,
} from '@divi/field-library';
import { mergeAttrs } from '@divi/module-utils';
import {
  type Module,
} from '@divi/types';

// Local dependencies.
import {OptimizeButtonItemAttrs} from "./types";
import {OptimizeButtonsAttrs} from "../OptimizeButtons/types";

export const SettingsContent = ({
    defaultSettingsAttrs,
  parentAttrs,
  }: Module.Settings.Panel.Props<OptimizeButtonItemAttrs, OptimizeButtonsAttrs>): ReactElement => {
  const defaultIconAttrs = mergeAttrs({
    defaultAttrs: defaultSettingsAttrs?.icon,
    attrs:        parentAttrs?.asMutable({ deep: true })?.icon,
  });

  return (
    <React.Fragment>
      <GroupContainer
        id="mainContent"
        title={__('Text', 'divi-optimaizer-modules')}
      >
        <FieldContainer
          attrName="button.innerContent"
          label={__('Button Text', 'divi-optimaizer-modules')}
          description={__('Input your value to action here.', 'divi-optimaizer-modules')}
          features={{
            sticky: false,
          }}
        >
          <TextContainer />
        </FieldContainer>
      </GroupContainer>

      <LinkGroup attrName='button.advanced.link' />
      <BackgroundGroup
        defaultGroupAttr={defaultSettingsAttrs?.module?.decoration?.background?.asMutable({ deep: true }) ?? {}}
      />
    </React.Fragment>
  );
}
