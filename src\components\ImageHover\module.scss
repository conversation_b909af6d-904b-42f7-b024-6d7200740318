// This style will render on Front-End and Visual Builder.

.dotm_image_hover {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.dotm_module__inner {
    width: 100%;
    height: 100%;
}

.dotm_module__image{
    position: relative;
    overflow: hidden;
    aspect-ratio: 1;
    cursor: pointer;
}
.dotm_module__image img{
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
    transition: all 1s cubic-bezier(0.4, 0, 0.2, 1);
}


/* 1. Advanced Glitch Effect */
.glitch-effect:hover img {
    animation: glitch 1s cubic-bezier(.25, .46, .45, .94) infinite;
}

@keyframes glitch {
    0% {
        transform: translate(0);
        filter: hue-rotate(0deg);
    }
    10% {
        transform: translate(-5px, 5px);
        filter: hue-rotate(90deg);
    }
    20% {
        transform: translate(5px, -5px);
        filter: hue-rotate(180deg);
    }
    30% {
        transform: translate(-3px, 3px);
        filter: hue-rotate(270deg);
    }
    40% {
        transform: translate(3px, -3px);
        filter: hue-rotate(360deg);
    }
    50% {
        transform: translate(0);
        filter: hue-rotate(0deg);
    }
}

/* 2. Ripple Effect */
.ripple-effect::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, transparent 30%, rgba(0,0,0,0.5) 70%);
    transform: scale(0);
    transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.ripple-effect:hover::before {
    transform: scale(2);
}


/* 4. Kaleidoscope Effect */
.kaleidoscope:hover img {
    animation: kaleidoscope 1s infinite linear;
}

@keyframes kaleidoscope {
    0% { filter: hue-rotate(0deg) saturate(1.5); }
    50% { filter: hue-rotate(180deg) saturate(2); }
    100% { filter: hue-rotate(360deg) saturate(1.5); }
}

/* 5. Liquid Distortion */
.liquid-distortion:hover img {
    filter: url('#liquid');
    transform: scale(1.1);
}

.liquid-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, 
        rgba(0,255,255,0.2), 
        rgba(255,0,255,0.2)
    );
    mix-blend-mode: hue;
    animation: liquid 2s infinite;
}

@keyframes liquid {
    0%, 100% { transform: translateY(-50%) rotate(0deg); }
    50% { transform: translateY(0%) rotate(180deg); }
}


/* 7. Neon Pulse */
.neon-pulse {
    position: relative;
}

.neon-pulse::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(45deg, #ff00ff, #00ffff);
    opacity: 0;
    transition: opacity 0.3s;
}

.neon-pulse:hover::before {
    opacity: 0.5;
    animation: neonPulse 1s infinite;
}

@keyframes neonPulse {
    0%, 100% { filter: brightness(1); }
    50% { filter: brightness(1.5); }
}

/* 8. Magnetic Corner Effect */
.magnetic-corner img {
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.magnetic-corner:hover img {
    // transform: perspective(700px) rotateX(10deg) rotateY(20deg);
}

/* 9. Time Portal Effect */
.time-portal {
    position: relative;
}

.time-portal::before {
    content: '';
    position: absolute;
    inset: 0;
    background: conic-gradient(
        transparent,
        rgba(0,255,255,0.3),
        transparent
    );
    animation: portalSpin 2s linear infinite;
    opacity: 0;
    transition: opacity 0.3s;
}

.time-portal:hover::before {
    opacity: 1;
}

@keyframes portalSpin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}


/* 11. Holographic Effect */
.holographic-horizontal::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(
        150deg,
        transparent 0%,
        rgba(255,255,255,0.3) 30%,
        rgba(255,255,255,0.6) 50%,
        rgba(255,255,255,0.3) 70%,
        transparent 100%
    );
    transform: translateY(-100%);
    transition: transform 0.5s;
}

.holographic-horizontal:hover::before {
    transform: translateY(100%);
}

.holographic-vertical::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(
        150deg,
        transparent 0%,
        rgba(255,255,255,0.3) 30%,
        rgba(255,255,255,0.6) 50%,
        rgba(255,255,255,0.3) 70%,
        transparent 100%
    );
    transform: translateX(-100%);
    transition: transform 0.5s;
}

.holographic-vertical:hover::before {
    transform: translateX(100%);
}

/* 12. Spiral Zoom */
.spiral-zoom:hover img {
    animation: spiralZoom 1s forwards;
}

@keyframes spiralZoom {
    0% { transform: scale(1) rotate(0deg); }
    100% { transform: scale(1.2) rotate(360deg); }
}

/* 13. Split Color Channels */
.split-color:hover img {
    animation: splitChannels 0.3s infinite;
}

@keyframes splitChannels {
    0%, 100% {
        filter: none;
    }
    33% {
        filter: sepia(100%) hue-rotate(90deg);
    }
    66% {
        filter: sepia(100%) hue-rotate(180deg);
    }
}

/* 14. Wave Distortion */
.wave-distortion:hover img {
    animation: waveEffect 2s ;
}

@keyframes waveEffect {
    0%, 100% {
        transform: translateY(0);
        filter: brightness(1);
    }
    25% {
        transform: translateY(5px);
        filter: brightness(1.2);
    }
    75% {
        transform: translateY(-50px);
        filter: brightness(0.8);
    }
}

.effect-border-1 {
    position: relative;
}

.effect-border-1::before,
.effect-border-1::after {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    opacity: 0;
    transition: width 1s, height 1s, opacity 1s;
}

.effect-border-1::before {
    top: 0;
    left: 0;
    border-top: 7px solid;
    border-top-color: #f70a0a;
    border-left: 7px solid;
    border-left-color: #f70a0a;
}

.effect-border-1::after {
    bottom: 0;
    right: 0;
    border-bottom: 7px solid #052d9b;
    border-right: 7px solid #0dcf1d;
}

.effect-border-1:hover::before,
.effect-border-1:hover::after {
    width: 100%;
    height: 100%;
    opacity: 1;
}

/* 10. Circular Reveal */
.effect-border-2 {
    position: relative;
    overflow: hidden;
}

.effect-border-2::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    // background: rgba(255,255,255);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.5s, height 0.5s;
}

.effect-border-2:hover::before {
    width: 200%;
    height: 200%;
}



/* 13. Diagonal Sweep */
.effect-13 {
    position: relative;
    overflow: hidden;
}

.effect-13::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: rgba(247, 13, 13, 0.2);
    transform: rotate(45deg);
    transition: transform 0.6s;
}

.effect-13:hover::after {
    transform: rotate(45deg) translate(50%, 50%);
}

.opacity-hover{
    background: rgba(131,0,233,0.25);
}

.opacity-hover img {
    opacity: 1;
	-webkit-transition: .3s ease-in-out;
	transition: .3s ease-in-out;
}
.opacity-hover:hover img {
    opacity: .5;
}

// blur hover
.blur-hover img {
	-webkit-filter: blur(3px);
	filter: blur(3px);
	-webkit-transition: .3s ease-in-out;
	transition: .3s ease-in-out;
}
.blur-hover img:hover {
	-webkit-filter: blur(0);
	filter: blur(0);
}
