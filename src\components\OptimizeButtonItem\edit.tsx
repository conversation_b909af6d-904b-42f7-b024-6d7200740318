// External Dependencies.
import React, { ReactElement } from "react";

// Divi Dependencies.
import { ModuleContainer } from "@divi/module";
import { generateDefaultAttrs } from "@divi/module-library";
import { getAttrByMode } from "@divi/module-utils";
import { processFontIcon } from "@divi/icon-library";
import { ModuleMetadata } from "@divi/types";

// Local Dependencies.
import { OptimizeButtonItemEditProps } from "./types";
import { ModuleStyles } from "./styles";
import { moduleClassnames } from "./module-classnames";
import { isEmpty, merge } from "lodash";
import parentMetadata from "../OptimizeButtons/module.json";

/**
 * OptimizeButtonItem edit component of visual builder.
 *
 * @since ??
 *
 * @param {OptimizeButtonItemEditProps} props React component props.
 *
 * @returns {ReactElement}
 */
export const OptimizeButtonItemEdit = (
  props: OptimizeButtonItemEditProps
): ReactElement => {
  const { attrs, elements, id, name, parentAttrs } = props;

  console.log(attrs, "child attrs");

  const parentDefaultAttrs = generateDefaultAttrs(
    parentMetadata as ModuleMetadata
  );
  const parentAttrsWithDefault = merge(parentDefaultAttrs, parentAttrs);

  const btn_hover_effect =
    attrs?.button?.advanced?.effect?.desktop?.value ??
    parentAttrs?.button?.advanced?.effect?.desktop?.value;

    const btn_link = attrs?.button?.advanced?.link?.desktop?.value?.url ?? '';
  const btn_target = attrs?.button?.advanced?.link?.desktop?.value?.target ?? '';

  return (
    <ModuleContainer
      attrs={attrs}
      parentAttrs={parentAttrs}
      elements={elements}
      id={id}
      name={name}
      stylesComponent={ModuleStyles}
      classnamesFunction={moduleClassnames}
      tag="div"
    >
      {elements.styleComponents({
        attrName: "module",
      })}

      {elements.render({
        attrName: "button",
        className: btn_hover_effect,
        htmlAttributes: {
          href: btn_link,
          target: btn_target ? "_blank" : "_self",
        },
      })}
    </ModuleContainer>
  );
};
