// External Dependencies.
import React, { ReactElement } from 'react';
import classnames from 'classnames';

// Divi Dependencies.
import { ModuleContainer } from '@divi/module';

// Local Dependencies.
import { ImageHoverEditProps } from './types';
import { ModuleStyles } from './styles';
import { moduleClassnames } from './module-classnames';
import { ModuleScriptData } from './module-script-data';

/**
 * ImageHover edit component of visual builder.
 *
 * @since ??
 *
 * @param {ImageHoverEditProps} props React component props.
 *
 * @returns {ReactElement}
 */
export const ImageHoverEdit = (props: ImageHoverEditProps): ReactElement => {
  const {
    attrs,
    elements,
    id,
    name,
  } = props;

  // Get image attributes.
  const imageSrc = attrs?.image?.innerContent?.desktop?.value?.src ?? ''
  const imageAlt = attrs?.image?.innerContent?.desktop?.value?.alt ?? '';
  const imgHoverClass = attrs?.hover?.decoration?.hoverClass?.desktop?.value ?? '';

  console.log(attrs, "attrs");

  return (
    <ModuleContainer
      attrs={attrs}
      elements={elements}
      id={id}
      name={name}
      stylesComponent={ModuleStyles}
      classnamesFunction={moduleClassnames}
      scriptDataComponent={ModuleScriptData}
    >
      {elements.styleComponents({
        attrName: 'module',
      })}
      <div className="dotm_module__inner">
        <div className={`dotm_module__image ${imgHoverClass}`}>
          <img src={imageSrc} alt={imageAlt} />
        </div>
      </div>
    </ModuleContainer>
  );
};
