/**
 * Utility functions for handling placeholder content fallbacks
 * to prevent undefined errors when @divi/module placeholderContent is not available
 */

// Default fallback values
export const defaultFallbacks = {
  title: 'Your Title Goes Here',
  body: 'Your content goes here. Lorem ipsum dolor sit amet, consectetur adipiscing elit.',
  image: {
    landscape: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTA4MCIgaGVpZ2h0PSI1NDAiIHZpZXdCb3g9IjAgMCAxMDgwIDU0MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KICAgIDxnIGZpbGw9Im5vbmUiIGZpbGwtcnVsZT0iZXZlbm9kZCI+CiAgICAgICAgPHBhdGggZmlsbD0iI0VCRUJFQiIgZD0iTTAgMGgxMDgwdjU0MEgweiIvPgogICAgICAgIDxwYXRoIGQ9Ik00NDUuNjQ5IDU0MGgtOTguOTk1TDE0NC42NDkgMzM3Ljk5NSAwIDQ4Mi42NDR2LTk4Ljk5NWwxMTYuMzY1LTExNi4zNjVjMTUuNjItMTUuNjIgNDAuOTQ3LTE1LjYyIDU2LjU2OCAwTDQ0NS42NSA1NDB6IiBmaWxsLW9wYWNpdHk9Ii4xIiBmaWxsPSIjMDAwIiBmaWxsLXJ1bGU9Im5vbnplcm8iLz4KICAgICAgICA8Y2lyY2xlIGZpbGwtb3BhY2l0eT0iLjA1IiBmaWxsPSIjMDAwIiBjeD0iMzMxIiBjeT0iMTQ4IiByPSI3MCIvPgogICAgICAgIDxwYXRoIGQ9Ik0xMDgwIDM3OXYxMTMuMTM3TDcyOC4xNjIgMTQwLjMgMzI4LjQ2MiA1NDBIMjE1LjMyNEw2OTkuODc4IDU1LjQ0NmMxNS42Mi0xNS42MiA0MC45NDgtMTUuNjIgNTYuNTY4IDBMMTA4MCAzNzl6IiBmaWxsLW9wYWNpdHk9Ii4yIiBmaWxsPSIjMDAwIiBmaWxsLXJ1bGU9Im5vbnplcm8iLz4KICAgIDwvZz4KPC9zdmc+Cg==',
    portrait: 'https://via.placeholder.com/600x800/cccccc/969696?text=Image+Placeholder',
  },
};

/**
 * Safely get placeholder title with fallback
 * @param placeholder - The placeholder object from @divi/module
 * @param fallback - Custom fallback text (optional)
 * @returns Safe title value
 */
export const getPlaceholderTitle = (placeholder: any, fallback?: string): string => {
  return placeholder?.title || fallback || defaultFallbacks.title;
};

/**
 * Safely get placeholder body with fallback
 * @param placeholder - The placeholder object from @divi/module
 * @param fallback - Custom fallback text (optional)
 * @returns Safe body value
 */
export const getPlaceholderBody = (placeholder: any, fallback?: string): string => {
  return placeholder?.body || fallback || defaultFallbacks.body;
};

/**
 * Safely get placeholder image with fallback
 * @param placeholder - The placeholder object from @divi/module
 * @param type - Image type ('landscape' or 'portrait')
 * @param fallback - Custom fallback URL (optional)
 * @returns Safe image URL
 */
export const getPlaceholderImage = (
  placeholder: any, 
  type: 'landscape' | 'portrait' = 'landscape',
  fallback?: string
): string => {
  return placeholder?.image?.[type] || fallback || defaultFallbacks.image[type];
};

/**
 * Create a safe placeholder object with all fallbacks
 * @param placeholder - The placeholder object from @divi/module
 * @param customFallbacks - Custom fallback values (optional)
 * @returns Safe placeholder object
 */
export const createSafePlaceholder = (placeholder: any, customFallbacks?: Partial<typeof defaultFallbacks>) => {
  const fallbacks = { ...defaultFallbacks, ...customFallbacks };
  
  return {
    title: placeholder?.title || fallbacks.title,
    body: placeholder?.body || fallbacks.body,
    image: {
      landscape: placeholder?.image?.landscape || fallbacks.image.landscape,
      portrait: placeholder?.image?.portrait || fallbacks.image.portrait,
    },
  };
};
