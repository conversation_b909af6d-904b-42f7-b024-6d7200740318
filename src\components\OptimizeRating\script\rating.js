export const rating = (props={}) => {

    const {maxRating, currentRating, containerId, use_tooltip} = props;
    
    // Clean up any existing event listeners to prevent duplicates
    const cleanupRating = () => {
      const ratingContainer = document.getElementById(containerId);
      if (ratingContainer) {
        const starsContainer = ratingContainer.querySelector('.dotm_optimize_rating_stars');
        if (starsContainer) {
          const newStarsContainer = starsContainer.cloneNode(false);
          starsContainer.parentNode.replaceChild(newStarsContainer, starsContainer);
        }
      }
    };
    
    // Initialize or reinitialize the rating module
    const initializeRating = () => {
      const ratingContainer = document.getElementById(containerId);
      if (ratingContainer) {
        initializeRatingModule(ratingContainer, maxRating, currentRating);
      }
    };
    
    // Clean up and initialize immediately if the DOM is already loaded
    if (document.readyState === 'complete' || document.readyState === 'interactive') {
      cleanupRating();
      initializeRating();
    } else {
      // Otherwise wait for DOMContentLoaded
      document.addEventListener('DOMContentLoaded', function() {
        cleanupRating();
        initializeRating();
      });
    }
      
      function initializeRatingModule(container, maxStarRating, initialRating) {
        var starsContainer = container.querySelector('.dotm_optimize_rating_stars');
        var localCurrentRating = initialRating || 0;
        
        // Create tooltip element
        const tooltip = document.createElement('div');
        tooltip.className = 'dotm_optimize_rating_tooltip';
        starsContainer.appendChild(tooltip);
        
        // Create initial stars
        renderStars();
        
        // Set initial rating
        if (localCurrentRating) {
          setRating(localCurrentRating);
        }
        
        // Add click handlers for stars
        starsContainer.addEventListener('click', function(e) {
          if (e.target.classList.contains('dotm_optimize_rating_star')) {
            var stars = Array.from(starsContainer.children);
            var starIndex = stars.indexOf(e.target);
            setRating(starIndex + 1);
          }
        });
        
        // Add mouseover event for tooltip
        starsContainer.addEventListener('mouseover', function() {
          updateTooltip();
        });
        
        function renderStars() {
          starsContainer.innerHTML = '';
          const starCount = maxStarRating || 5; // Default to 5 if maxStarRating is not provided
          
          // Re-append tooltip after clearing stars container
          if (use_tooltip === 'on') {
            starsContainer.appendChild(tooltip);
          }
          // starsContainer.appendChild(tooltip);
          
          for (var i = 0; i < starCount; i++) {
            var star = document.createElement('span');
            star.className = 'dotm_optimize_rating_star';
            star.innerHTML = "&#xf005;";
            starsContainer.appendChild(star);
          }
        }
        
        function updateTooltip() {
          const maxValue = maxStarRating || 5;
          tooltip.textContent = `${localCurrentRating}/${maxValue}`;
        }
        
        function highlightStars(count) {
          var stars = starsContainer.querySelectorAll('.dotm_optimize_rating_star');
          stars.forEach(function(star, index) {
            if (index < Math.floor(count)) {
              star.classList.add('dotm_optimize_rating_star_filled');
            } else {
              star.classList.remove('dotm_optimize_rating_star_filled');
            }
            
            // Handle partial stars for decimal ratings
            if (index === Math.floor(count) && count % 1 !== 0) {
              star.classList.add('dotm_optimize_rating_star_filled');
            }
          });
          
          // Update tooltip text whenever stars are highlighted
          updateTooltip();
        }
        
        function setRating(rating) {
          const maxValue = maxStarRating || 5;
          localCurrentRating = Math.min(Math.max(rating, 0), maxValue);
          highlightStars(localCurrentRating);
        }
        
        // Initialize with the provided rating if valid
        if (initialRating !== undefined) {
          var inputValue = parseFloat(initialRating);
          if (!isNaN(inputValue) && inputValue >= 0 && inputValue <= (maxStarRating || 5)) {
            setRating(inputValue);
          }
        }
      }

}