.dotm_optimize_card {
    .dotm_optimize_card_container {
        display: flex;
        transition: transform 0.6s ease;

        .dotm_optimize_card_inner_image_wrapper {
            display: flex;

            .dotm_optimize_card_image_container {
                position: relative;
                overflow: hidden;

                .dotm_optimize_card_image {
                    transition: transform 0.3s ease;
                    width: 100%;
                    height: 100%;
                }

                .dotm_optimize_card_image_label {
                    display: inline-block;
                    position: absolute;
                }
            }
        }

        .dotm_optimize_card_icon_container {
            width: 100%;
            .dotm_optimize_card_icon {
                display: inline-block;
            }
        }

        .dotm_optimize_card_content_container {
            width: 100%;

            .dotm_optimize_card_btn_container {

                .dotm_optimize_card_btn {
                    display: inline-block;
                }
            }
        }
    }

    .dotm_optimize_card_shadow {
        &:hover {
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2) !important;
        }
    }

    .dotm_optimize_card_slide_up {
        &:hover {
            transform: translateY(-10px);
        }
    }

    .dotm_optimize_card_rotate {
        &:hover {
            transform: rotate(2deg);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }
    }

    .dotm_optimize_card_scale {
        &:hover {
            transform: scale(1.1);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }
    }

    .dotm_optimize_card_skew {
        &:hover {
            transform: skewY(-2deg);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.2);
        }
    }
}